/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing,
 * software distributed under the License is distributed on an
 * "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 * KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations
 * under the License.
 */
package org.apache.fineract.cn.originate.api.v1.client;

import io.swagger.annotations.ApiParam;
import org.apache.fineract.cn.originate.api.v1.config.OriginateFeignClientConfig;
import org.apache.fineract.cn.originate.api.v1.domain.businessProcess.BusinessProcess;
import org.apache.fineract.cn.originate.api.v1.domain.configurationDetails.ConfigurationDetails;
import org.apache.fineract.cn.originate.api.v1.domain.customer.Customer;
import org.apache.fineract.cn.originate.api.v1.domain.deal.DealCustomer;
import org.apache.fineract.cn.originate.api.v1.domain.deal.Deal;
import org.apache.fineract.cn.originate.api.v1.domain.deal.DealScore;
import org.apache.fineract.cn.originate.api.v1.domain.deal.search.DealEvent;
import org.apache.fineract.cn.originate.api.v1.domain.entity.EntityDefinition;
import org.apache.fineract.cn.originate.api.v1.domain.reqdoc.DocumentDomain;
import org.springframework.cloud.netflix.feign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

@SuppressWarnings("unused")
@FeignClient(value="originate-v1", path="/originate/v1", configuration = OriginateFeignClientConfig.class)
public interface OriginateManager {

  @RequestMapping(
          value = "/requestdocument/{id}",
          method = RequestMethod.GET,
          consumes = MediaType.ALL_VALUE,
          produces = MediaType.APPLICATION_JSON_VALUE
  )
  public
  @ResponseBody
  DocumentDomain.DocumentRequest getDealRequestDocument(@PathVariable("id") final Long requestId);


  @RequestMapping(
          value = "/deal/name/{id}",
          method = RequestMethod.GET,
          consumes = MediaType.ALL_VALUE,
          produces = MediaType.APPLICATION_JSON_VALUE
  )
  public
  @ResponseBody
  DealCustomer getDealName(@PathVariable("id") final Long id);

  @RequestMapping(
          value = "/deal/userscore/id/{dealScoreId}",
          method = RequestMethod.GET,
          consumes = MediaType.APPLICATION_JSON_VALUE,
          produces = MediaType.APPLICATION_JSON_VALUE
  )
  public
  @ResponseBody
  DealScore getDealScoreEntity(@PathVariable("dealScoreId") final Long dealScoreId);

  @RequestMapping(
          value = "/deal/{id}",
          method = RequestMethod.GET,
          consumes = MediaType.ALL_VALUE,
          produces = MediaType.APPLICATION_JSON_VALUE
  )
  public
  @ResponseBody
  Deal getDeal(@PathVariable("id") final Long id);

/*  @RequestMapping(
          value = "/requestdocument/id/{id}",
          method = RequestMethod.GET,
          consumes = MediaType.ALL_VALUE,
          produces = MediaType.APPLICATION_JSON_VALUE
  )
  public
  @ResponseBody
  DealRequestDocument getRequestDocument(@PathVariable("id") final Long dealId);  */

  @RequestMapping(
          value = "/requestdocument/{id}",
          method = RequestMethod.GET,
          consumes = MediaType.ALL_VALUE,
          produces = MediaType.APPLICATION_JSON_VALUE
  )
  public
  @ResponseBody
  DocumentDomain.DocumentRequest getRequestDocument(@PathVariable("id") final Long requestId);

  @RequestMapping(value = "/eventDetails/{id}", method = RequestMethod.GET, consumes = MediaType.ALL_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
  public
  @ResponseBody
  DealEvent retrieveEventDetails(@PathVariable("id") final Long id, @RequestParam(name = "size", defaultValue = "25",
          required = false) int size);

  @RequestMapping(
          value = "/business-process/{id}",
          method = RequestMethod.GET,
          consumes = MediaType.ALL_VALUE,
          produces = MediaType.APPLICATION_JSON_VALUE
  )
  public
  @ResponseBody
  BusinessProcess findBusinessProcessById(@PathVariable("id") final Long id);

    @RequestMapping(
            value = "/customer/{id}",
            method = RequestMethod.GET,
            consumes = MediaType.ALL_VALUE,
            produces = MediaType.APPLICATION_JSON_VALUE
    )
    public
    @ResponseBody
    Customer getEntityDetails(@PathVariable("id") final String entityId);

  @RequestMapping(
          value = "/configuration-details/configurable-identifier/{configurableIdentifier}",
          method = RequestMethod.GET,
          consumes = MediaType.APPLICATION_JSON_VALUE,
          produces = MediaType.APPLICATION_JSON_VALUE
  )
  public
  @ResponseBody
  ConfigurationDetails retrieveUsingConfigurationIdentifier(@PathVariable("configurableIdentifier") final String configurableIdentifier);

  @RequestMapping(
          value = "/business-process/name/{name}",
          method = RequestMethod.GET,
          consumes = MediaType.ALL_VALUE,
          produces = MediaType.APPLICATION_JSON_VALUE
  )
  @ResponseBody
  BusinessProcess findBusinessProcessByName(@PathVariable ("name") final String name);

  @RequestMapping(
          value = "/entity/entity-definition-by-identifier",
          method = RequestMethod.GET,
          consumes = MediaType.ALL_VALUE,
          produces = MediaType.APPLICATION_JSON_VALUE
  )
  @ResponseBody
  EntityDefinition getEntityDefinitionByIdOrName(@ApiParam(value = "Id") @RequestParam(name = "id", required = false) String id,
                                                  @ApiParam(value = "Name") @RequestParam(name = "name", required = false) String name,
                                                  @ApiParam(value = "Entity Type") @RequestParam(name = "entityType", required = false) String entityType,
                                                  @ApiParam(value = "Entity Sub Type") @RequestParam(name = "subType", required = false) String subType);
}
