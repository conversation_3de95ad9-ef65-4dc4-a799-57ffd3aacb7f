package org.apache.fineract.cn.originate.api.v1.domain.deal;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.fineract.cn.originate.api.v1.serializer.IdDecryptDeserializer;
import org.apache.fineract.cn.originate.api.v1.serializer.IdEncryptSerializer;

@Data
@ApiModel(value = "DEal Customer Domain", description = "Deal Customer Domain Class")
public class DealCustomer {

	@ApiModelProperty(value = "ID")
	@JsonSerialize(using = IdEncryptSerializer.class)
	@JsonDeserialize(using = IdDecryptDeserializer.class)
    private Long id;
	@ApiModelProperty(value = "Entity ID")
	@JsonSerialize(using = IdEncryptSerializer.class)
	@JsonDeserialize(using = IdDecryptDeserializer.class)
    private Long entityId;
	@ApiModelProperty(value = "Customer Name")
    private String customerName;
	@ApiModelProperty(value = "Company Flag (True/False)")
    private Boolean companyFlag;
	@ApiModelProperty(value = "Customer Type")
    private String customerType;
	@ApiModelProperty(value = "Created By")
    private String createdBy;
	@ApiModelProperty(value = "Created Date")
    private String createdDate;
	@ApiModelProperty(value = "Modified By")
    private String modifiedBy;
	@ApiModelProperty(value = "Modified Date")
    private String modifiedDate;
	@ApiModelProperty(value = "Co-Applicant Flag (True/False)")
    private Boolean coApplicantFlag;    

	public DealCustomer() {
		super();		
	}


}
