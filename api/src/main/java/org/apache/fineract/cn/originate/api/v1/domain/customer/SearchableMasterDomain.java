package org.apache.fineract.cn.originate.api.v1.domain.customer;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.fineract.cn.originate.api.v1.serializer.IdDecryptDeserializer;
import org.apache.fineract.cn.originate.api.v1.serializer.IdEncryptSerializer;

@Data
@ApiModel(value = "Searchable Master Domain", description = "Searchable Master Domain Class")
public class SearchableMasterDomain {
	@ApiModelProperty(value = "ID")
//	@JsonSerialize(using = IdEncryptSerializer.class)
//	@JsonDeserialize(using = IdDecryptDeserializer.class)
	private String id;
	@ApiModelProperty(value = "Name")
	private String name;
	@ApiModelProperty(value = "Details JSON Object")
	private  JsonNode details;

	public SearchableMasterDomain() {
		super();
	}

	public SearchableMasterDomain(String id, String name) {
		super();
		this.id = id;
		this.name = name;
	}

	public SearchableMasterDomain(String id, String name,JsonNode details) {
		this.id = id;
		this.name = name;
		this.details = details;
	}

}
