/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing,
 * software distributed under the License is distributed on an
 * "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 * KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations
 * under the License.
 */
package org.apache.fineract.cn.originate.service.internal.command;

import com.centelon.finnate.workflowengine.api.v1.domain.WorkflowDomain;
import lombok.Data;
import org.apache.fineract.cn.originate.api.v1.domain.businessProcess.BusinessProcessWithRelatedDetails;
import org.apache.fineract.cn.originate.api.v1.domain.copyconfiguration.CopyConfigurationDomain;
import org.apache.fineract.cn.originate.api.v1.domain.entity.EntityDefinitionWithRelatedDetails;

import java.util.List;

@Data
public class UpdateRuleConfigurationCommand {

  private final CopyConfigurationDomain copyConfigurationDomain;
  private  BusinessProcessWithRelatedDetails businessProcessWithRelatedDetails;
  private  EntityDefinitionWithRelatedDetails entityDefinitionWithRelatedDetails;
  private final List<WorkflowDomain.Workflow> specificWorkflowList;
  private final String accessToken;
  public String zcpUrl, workflowUrl;

  public UpdateRuleConfigurationCommand(final CopyConfigurationDomain copyConfigurationDomain,
                                        BusinessProcessWithRelatedDetails businessProcessWithRelatedDetails,
                                        List<WorkflowDomain.Workflow> specificWorkflowList, String accessToken,
                                        String zcpUrl, String workflowUrl) {
    super();
    this.copyConfigurationDomain = copyConfigurationDomain;
    this.businessProcessWithRelatedDetails = businessProcessWithRelatedDetails;
    this.specificWorkflowList = specificWorkflowList;
    this.accessToken = accessToken;
    this.zcpUrl = zcpUrl;
    this.workflowUrl = workflowUrl;
  }

  public UpdateRuleConfigurationCommand(final CopyConfigurationDomain copyConfigurationDomain,
                                        EntityDefinitionWithRelatedDetails entityDefinitionWithRelatedDetails,
                                        List<WorkflowDomain.Workflow> specificWorkflowList, String accessToken,
                                        String zcpUrl, String workflowUrl) {
    super();
    this.copyConfigurationDomain = copyConfigurationDomain;
    this.entityDefinitionWithRelatedDetails = entityDefinitionWithRelatedDetails;
    this.specificWorkflowList = specificWorkflowList;
    this.accessToken = accessToken;
    this.zcpUrl = zcpUrl;
    this.workflowUrl = workflowUrl;
  }

  public List<WorkflowDomain.Workflow> getWorkflowList(){
    return specificWorkflowList;
  }

  @Override
public String toString() {
	return "UpdateCopyConfigurationCommand [copyConfigurationDomain=" + copyConfigurationDomain + "]";
}
  
}
