/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing,
 * software distributed under the License is distributed on an
 * "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 * KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations
 * under the License.
 */
package org.apache.fineract.cn.originate.service.dealLeadAllocation.rest;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

import javax.validation.Valid;

import io.swagger.annotations.*;
import org.apache.fineract.cn.anubis.annotation.AcceptedTokenType;
import org.apache.fineract.cn.anubis.annotation.Permittable;
import org.apache.fineract.cn.command.gateway.CommandGateway;
import org.apache.fineract.cn.lang.ServiceException;
import org.apache.fineract.cn.originate.api.v1.PermittableGroupIds;
import org.apache.fineract.cn.originate.api.v1.domain.dealLeadAllocation.DealTeamLeadStoreConfiguration;
import org.apache.fineract.cn.originate.api.v1.validation.ExceptionConstants.DealConstants;
import org.apache.fineract.cn.originate.service.ServiceConstants;
import org.apache.fineract.cn.originate.service.internal.dealLeadAllocation.command.AddDealTeamLeadConfifurationCommand;
import org.apache.fineract.cn.originate.service.internal.dealLeadAllocation.command.DeleteDealTeamLeadConfifurationCommand;
import org.apache.fineract.cn.originate.service.internal.dealLeadAllocation.command.UpdateDealTeamLeadConfifurationCommand;
import org.apache.fineract.cn.originate.service.internal.dealLeadAllocation.service.DealTeamLeadStoreService;
import org.slf4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

@SuppressWarnings("unused")
@RestController
@RequestMapping("/")
@Api(value = "Deal Team Lead Store Allocation Rest Controller",
        description = "Provides RESTful endpoints for managing Deal Team Lead allocations to stores. These endpoints " +
                "allow for the creation, retrieval, updating, and deletion of allocations.  They facilitate " +
                "the assignment of Deal Team Leads to specific stores, enabling efficient management of " +
                "sales and related activities.  The API supports various operations, including retrieving " +
                "allocations by ID, workflow ID, and retrieving a list of all allocations.  Appropriate " +
                "authorization is required to access these endpoints.",
        tags = {"Deal Team Lead Store Allocation Rest Controller"}
)
public class DealTeamLeadStoreAllocationRestController {

  private final Logger logger;
  private final CommandGateway commandGateway;
  private final DealTeamLeadStoreService dealTeamLeadStoreService;

  @Autowired
  public DealTeamLeadStoreAllocationRestController(@Qualifier(ServiceConstants.LOGGER_NAME) final Logger logger,
                                   final CommandGateway commandGateway,
                                   final DealTeamLeadStoreService dealTeamLeadStoreService) {
    super();
    this.logger = logger;
    this.commandGateway = commandGateway;
    this.dealTeamLeadStoreService = dealTeamLeadStoreService;
  }

  @Permittable(value = AcceptedTokenType.TENANT, groupId = PermittableGroupIds.TEAM_LEAD_ALLOCATION_CONFIG)
  @RequestMapping(
          value = "/teamleadallocation",
          method = RequestMethod.GET,
          consumes = MediaType.ALL_VALUE,
          produces = MediaType.APPLICATION_JSON_VALUE
  )
  @ApiOperation(value = "Get All Team Lead Allocation List",
          notes = "Retrieves a list of all Team Lead allocations. This endpoint returns comprehensive information " +
                  "about all existing allocations.  The results may be paginated.  If no allocations exist, an " +
                  "empty list (or a 204 No Content) will be returned. This operation requires appropriate " +
                  "authorization.",
          httpMethod = "GET")
  @ApiResponses(value = {
          @ApiResponse(code = 200, message = "Team Lead allocation list retrieved successfully. The response body contains the list of allocations."),
          @ApiResponse(code = 400, message = "Bad Request. Invalid request parameters provided (e.g., for pagination). Check the request for correctness and format."), // For pagination issues
          @ApiResponse(code = 401, message = "Unauthorized. Authentication failed or missing. Ensure you are logged in and have the necessary permissions."),
          @ApiResponse(code = 403, message = "Forbidden. The authenticated user does not have permission to access Team Lead allocations."),
          @ApiResponse(code = 500, message = "Internal Server Error. An unexpected error occurred on the server. Please contact the system administrator.")
  })
  public
  @ResponseBody
  List<DealTeamLeadStoreConfiguration> findAllTeamLeads() {
    logger.debug("Retrieves the list of all Team Lead allocations");
    return this.dealTeamLeadStoreService.findAllTeamLeads();
  }

  @Permittable(value = AcceptedTokenType.TENANT, groupId = PermittableGroupIds.TEAM_LEAD_ALLOCATION_CONFIG)
  @RequestMapping(
          value = "/teamleadallocation/{id}",
          method = RequestMethod.GET,
          consumes = MediaType.ALL_VALUE,
          produces = MediaType.APPLICATION_JSON_VALUE
  )
  @ApiOperation(value = "Get Team Lead Allocation By ID",
          notes = "Retrieves a specific Team Lead allocation by its unique ID. This endpoint returns detailed " +
                  "information about the requested allocation. If an allocation with the given ID does not exist, " +
                  "a 404 Not Found error will be returned. This operation requires appropriate authorization.",
          httpMethod = "GET"
  )
  @ApiResponses(value = {
          @ApiResponse(code = 200, message = "Team Lead allocation retrieved successfully. The response body contains the allocation details."),
          @ApiResponse(code = 400, message = "Bad Request. Invalid allocation ID provided. Check the ID for correctness and format."),
          @ApiResponse(code = 401, message = "Unauthorized. Authentication failed or missing. Ensure you are logged in and have the necessary permissions."),
          @ApiResponse(code = 403, message = "Forbidden. The authenticated user does not have permission to access Team Lead allocations."),
          @ApiResponse(code = 404, message = "Team Lead allocation not found. No allocation exists for the specified ID."),
          @ApiResponse(code = 500, message = "Internal Server Error. An unexpected error occurred on the server. Please contact the system administrator.")
  })
  public
  @ResponseBody
  ResponseEntity<DealTeamLeadStoreConfiguration> getTeamLeadDetails(@ApiParam(value = "team Lead ID", required = true) @PathVariable("id") final Long id) {
    logger.debug("Retrieves a specific Team Lead allocation by its unique ID {}",id);
    return this.dealTeamLeadStoreService.findById(id)
            .map(ResponseEntity::ok)
            .orElseThrow(() -> ServiceException.notFound("Instance with team lead " + id + " doesn't exist."));
  }
  
  @Permittable(value = AcceptedTokenType.TENANT, groupId = PermittableGroupIds.TEAM_LEAD_ALLOCATION_CONFIG)
  @RequestMapping(
          value = "/teamleadallocation/workflow/{id}",
          method = RequestMethod.GET,
          consumes = MediaType.ALL_VALUE,
          produces = MediaType.APPLICATION_JSON_VALUE
  )
  @ApiOperation(value = "Get Team Lead Allocation By Workflow ID",
          notes = "Retrieves the Team Lead allocation associated with a specific Workflow ID.  This endpoint returns " +
                  "information about the Team Lead assigned to handle the given workflow.  If no allocation is found " +
                  "for the specified Workflow ID, a 404 Not Found error will be returned. This operation requires " +
                  "appropriate authorization.",
          httpMethod = "GET"
  )
  @ApiResponses(value = {
          @ApiResponse(code = 200, message = "Team Lead allocation retrieved successfully. The response body contains the allocation details."),
          @ApiResponse(code = 400, message = "Bad Request. Invalid Workflow ID provided. Check the ID for correctness and format."),
          @ApiResponse(code = 401, message = "Unauthorized. Authentication failed or missing. Ensure you are logged in and have the necessary permissions."),
          @ApiResponse(code = 403, message = "Forbidden. The authenticated user does not have permission to access Team Lead allocations."),
          @ApiResponse(code = 404, message = "Team Lead allocation not found. No allocation exists for the specified Workflow ID."),
          @ApiResponse(code = 500, message = "Internal Server Error. An unexpected error occurred on the server. Please contact the system administrator.")
  })
  public
  @ResponseBody
  List<DealTeamLeadStoreConfiguration> findAllTeamLeadsByWorkflowId(@ApiParam(value = "Workflow ID", required = true) @PathVariable("id") final Long id) {
    logger.debug("Retrieves the Team Lead allocation associated with a specific Workflow ID {}",id);
    return this.dealTeamLeadStoreService.findAllTeamLeadsByBusinessProcessId(id);
  }
  
  
  @Permittable(value = AcceptedTokenType.TENANT, groupId = PermittableGroupIds.TEAM_LEAD_ALLOCATION_CONFIG)
  @RequestMapping(
      value = "/teamleadallocation",
      method = RequestMethod.POST,
      consumes = MediaType.APPLICATION_JSON_VALUE,
      produces = MediaType.APPLICATION_JSON_VALUE
  )
  @ApiOperation(value = "Create Deal Team Lead Configuration",
          notes = "Creates a new Deal Team Lead configuration. The request body should contain the data for the new " +
                  "configuration. Upon successful creation, the API will return the newly created configuration, " +
                  "including any server-generated fields (e.g., ID, timestamps). This operation requires appropriate " +
                  "authorization.",
          httpMethod = "POST"
  )
  @ApiResponses(value = {
          @ApiResponse(code = 201, message = "Deal Team Lead configuration created successfully. The response body contains the newly created configuration."),
          @ApiResponse(code = 202, message = "Accepted. The request has been accepted for processing, but the processing has not been completed. The final status will be available later."),
          @ApiResponse(code = 400, message = "Bad Request. Invalid request body or parameters provided. Check the request for correctness and format. Specific error messages may be provided in the response body indicating which fields are invalid and why."),
          @ApiResponse(code = 401, message = "Unauthorized. Authentication failed or missing. Ensure you are logged in and have the necessary permissions."),
          @ApiResponse(code = 403, message = "Forbidden. The authenticated user does not have permission to create a Deal Team Lead configuration."),
          @ApiResponse(code = 500, message = "Internal Server Error. An unexpected error occurred on the server. Please contact the system administrator.")
  })
  public
  @ResponseBody
  ResponseEntity<?> createDealTeamLeadConfifuration(@ApiParam(value = "DealTeamLeadStoreConfiguration Domain", required = true) @RequestBody @Valid final DealTeamLeadStoreConfiguration dealTeamLeadStoreConfiguration) throws InterruptedException {
    logger.debug("Trying to create a new Deal Team Lead configuration");
    if(dealTeamLeadStoreService.findByUserName(dealTeamLeadStoreConfiguration.getUserName()).isPresent()){
	        throw ServiceException.badRequest("UserName '" + dealTeamLeadStoreConfiguration.getUserName() + "' Already exists.");
	      }
      logger.info("processing the command for adding the Deal Team Lead Configuration");
		this.commandGateway.process(new AddDealTeamLeadConfifurationCommand(dealTeamLeadStoreConfiguration));
    return ResponseEntity.accepted().build();
  }

  @Permittable(value = AcceptedTokenType.TENANT, groupId = PermittableGroupIds.TEAM_LEAD_ALLOCATION_CONFIG)
  @RequestMapping(
          value = "/teamleadallocation/{id}",
          method = RequestMethod.PUT,
          consumes = MediaType.APPLICATION_JSON_VALUE,
          produces = MediaType.APPLICATION_JSON_VALUE
  )
  @ApiOperation(value = "Update Deal Team Lead Configuration",
          notes = "Updates the configuration for Deal Team Leads. This operation allows modification of settings " +
                  "related to Deal Team Leads, such as default allocation rules, notification preferences, or other " +
                  "configuration parameters.  The request body should contain the updated configuration data.  " +
                  "Partial updates are supported; only the fields provided in the request will be modified.  " +
                  "This operation requires appropriate authorization.",
          httpMethod = "PUT"
  )
  @ApiResponses(value = {
          @ApiResponse(code = 200, message = "Deal Team Lead configuration updated successfully."),
          @ApiResponse(code = 400, message = "Bad Request. Invalid request body or parameters provided. Check the request for correctness and format.  Specific error messages may be provided in the response body indicating which fields are invalid and why."), // More specific
          @ApiResponse(code = 401, message = "Unauthorized. Authentication failed or missing. Ensure you are logged in and have the necessary permissions."), // More specific
          @ApiResponse(code = 403, message = "Forbidden. The authenticated user does not have permission to update the Deal Team Lead configuration."), // More specific
          @ApiResponse(code = 404, message = "Configuration not found. The specified configuration resource could not be found.  This might indicate an attempt to update a non-existent configuration."), // More specific
          @ApiResponse(code = 500, message = "Internal Server Error. An unexpected error occurred on the server. Please contact the system administrator.")
  })
  public
  @ResponseBody
  ResponseEntity<?> updateDealTeamLeadConfifuration(@ApiParam(value = "Deal Team Lead Configuration ID", required = true) @PathVariable final Long id,
                                                    @ApiParam(value = "DealTeamLeadStoreConfiguration Domain", required = true) @RequestBody DealTeamLeadStoreConfiguration dealTeamLeadStoreConfiguration){
    logger.debug("Trying to updates the configuration for Deal Team Leads for the ID: {}",id);
    Optional<DealTeamLeadStoreConfiguration> retrievedApplicationLabel = dealTeamLeadStoreService.findById(id);
    if (!retrievedApplicationLabel.isPresent()) {
      throw ServiceException.badRequest(DealConstants.CHECK_TEAM_LEAD);
    }
    logger.debug("Processing the update command for the given ID {}:", id);
    this.commandGateway.process(new UpdateDealTeamLeadConfifurationCommand(id, dealTeamLeadStoreConfiguration));
    return ResponseEntity.accepted().build();
  }

  @Permittable(value = AcceptedTokenType.TENANT, groupId = PermittableGroupIds.TEAM_LEAD_ALLOCATION_CONFIG)
  @RequestMapping(
          value = "/teamleadallocation/{id}",
          method = RequestMethod.DELETE,
          consumes = MediaType.APPLICATION_JSON_VALUE,
          produces = MediaType.APPLICATION_JSON_VALUE
  )
  @ApiOperation(value = "Delete Deal Team Lead allocation",
          notes = "Deletes the allocation of a Deal Team Lead.  This operation requires appropriate authorization.  " +
                  "Upon successful deletion, the specified Deal Team Lead will no longer be associated with the " +
                  "relevant deal.  If the allocation does not exist, a 404 Not Found error will be returned.  " +
                  "Consider the impact of removing a Deal Team Lead before proceeding, as it may affect deal " +
                  "management and reporting.  Ensure you have the correct Deal Team Lead ID and deal identifier.",
          httpMethod = "DELETE"
  )
  @ApiResponses(value = {
          @ApiResponse(code = 200, message = "Deal Team Lead allocation deleted successfully."),
          @ApiResponse(code = 400, message = "Bad Request. Invalid request parameters provided. Check the Deal Team Lead ID and deal identifier for correctness and format."), // Added for clarity
          @ApiResponse(code = 401, message = "Unauthorized. Authentication failed or missing.  Ensure you are logged in and have the necessary permissions."), // More specific than 403
          @ApiResponse(code = 403, message = "Forbidden. The authenticated user does not have permission to delete this allocation."), // More specific
          @ApiResponse(code = 404, message = "Deal Team Lead allocation not found. The specified Deal Team Lead or deal does not exist, or the allocation between them could not be found."),
          @ApiResponse(code = 500, message = "Internal Server Error. An unexpected error occurred on the server. Please contact the system administrator.")
  })
  public
  @ResponseBody
  ResponseEntity<Void> deleteDealTeamLeadConfifuration(@ApiParam(value = "Deal Team Lead Configuration ID", required = true) @PathVariable final Long id){
    logger.debug("Trying to delete the allocation of a Deal Team Lead for the given ID {}",id);
    final DeleteDealTeamLeadConfifurationCommand deleteDealTeamLeadConfifurationCommand = new DeleteDealTeamLeadConfifurationCommand(id);
    logger.info("calling the command for deleting the Deal Team Lead Configuration");
    this.commandGateway.process(deleteDealTeamLeadConfifurationCommand);
    return ResponseEntity.accepted().build();
  }
}
