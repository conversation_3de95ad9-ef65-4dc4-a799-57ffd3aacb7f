package org.apache.fineract.cn.originate.service.internal.repository.ConfigurationDetails;

import org.springframework.data.jpa.repository.JpaRepository;

import java.util.Optional;


public interface ConfigurationDetailsRepository extends JpaRepository<ConfigurationDetailsEntity, Long> {

    Optional<ConfigurationDetailsEntity> findById(Long id);
    Optional<ConfigurationDetailsEntity> findByConfigIdentifier(String configIdentifier);

}
