package org.apache.fineract.cn.originate.service.rest;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import io.swagger.annotations.ApiResponses;
import io.swagger.annotations.ApiResponse;
import org.apache.fineract.cn.anubis.annotation.AcceptedTokenType;
import org.apache.fineract.cn.anubis.annotation.Permittable;
import org.apache.fineract.cn.command.domain.CommandCallback;
import org.apache.fineract.cn.command.domain.CommandProcessingException;
import org.apache.fineract.cn.command.gateway.CommandGateway;
import org.apache.fineract.cn.lang.ServiceException;
import org.apache.fineract.cn.originate.api.v1.PermittableGroupIds;
import org.apache.fineract.cn.originate.api.v1.domain.configurableDashboard.CloneDashboard;
import org.apache.fineract.cn.originate.api.v1.domain.configurableDashboard.Dashboard;
import org.apache.fineract.cn.originate.api.v1.domain.configurableDashboard.DashboardOrder;
import org.apache.fineract.cn.originate.api.v1.validation.ExceptionConstants.DashboardConstants;
import org.apache.fineract.cn.originate.api.v1.validation.ExceptionConstants.DealConstants;
import org.apache.fineract.cn.originate.service.ServiceConstants;
import org.apache.fineract.cn.originate.service.internal.command.AddDashboardCommand;
import org.apache.fineract.cn.originate.service.internal.command.UpdateDashboardCommand;
import org.apache.fineract.cn.originate.service.internal.command.UpdateDashboardOrderCommand;
import org.apache.fineract.cn.originate.service.internal.command.CloneDashboardCommand;
import org.apache.fineract.cn.originate.service.internal.command.DeleteDashboardCommand;
import org.apache.fineract.cn.originate.service.internal.service.DashboardService;
import org.apache.fineract.cn.originate.service.internal.service.externalservice.IdentityService;
import org.slf4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.ExecutionException;

@RestController
@RequestMapping("/")
@Api(
		value = "Dashboard REST Controller",
		description = "REST API endpoints for managing and retrieving dashboard-related data. " +
				"Provides access to business insights, analytics, statistics, and user-specific summaries. " +
				"Supports filtering, sorting, and paginated responses for efficient data retrieval.",
		tags = {"Dashboard Rest Controller"}
)
public class DashboardRestController {
	private final Logger logger;
	private final CommandGateway commandGateway;
	private final DashboardService dashboardService;
	private final IdentityService identityService;

	@Autowired
	public DashboardRestController(@Qualifier(ServiceConstants.LOGGER_NAME) Logger logger,
                                   final CommandGateway commandGateway,
                                   final DashboardService dashboardService, IdentityService identityService) {
		this.logger = logger;
		this.commandGateway = commandGateway;
		this.dashboardService = dashboardService;
        this.identityService = identityService;
    }
    //Get all dashboard
    @Permittable(value = AcceptedTokenType.TENANT, groupId = PermittableGroupIds.CONFIGURATION_DASHBOARD_MANAGEMENT)
    @RequestMapping(
            value = "/dashboard",
            method = RequestMethod.GET,
            consumes = MediaType.ALL_VALUE,
            produces = MediaType.APPLICATION_JSON_VALUE
    )
	@ApiOperation(
			value = "Get List of All Configurable Dashboards",
			notes = "Retrieves a list of all dashboards that can be configured by the user. " +
					"Supports filtering and sorting based on user preferences.",
			httpMethod = "GET"
	)
	@ApiResponses(value = {
			@ApiResponse(code = 200, message = "OK - Successfully retrieved the list of dashboards"),
			@ApiResponse(code = 403, message = "Forbidden - You do not have permission to access this resource"),
			@ApiResponse(code = 404, message = "Not Found - The requested resource does not exist"),
			@ApiResponse(code = 500, message = "Internal Server Error - An unexpected error occurred")
	})
    public
    @ResponseBody
	List<Dashboard> findAllConfigurableDashboard() {
        logger.info("Received GET ALL Dashboard Request...");
        return this.dashboardService.findAllConfigurableDashboard();
    }

    //Get By dashboard name
    @Permittable(value = AcceptedTokenType.TENANT, groupId = PermittableGroupIds.CONFIGURATION_DASHBOARD_MANAGEMENT)
    @RequestMapping(
            value = "/dashboard/name/{dashboardName}",
            method = RequestMethod.GET,
            consumes = MediaType.ALL_VALUE,
            produces = MediaType.APPLICATION_JSON_VALUE
    )
	@ApiOperation(
			value = "Get Configurable Dashboard By Name",
			notes = "Retrieves details of a specific configurable dashboard based on the provided name. " +
					"Dashboards contain business insights, analytics, and reports customized for user needs. " +
					"This endpoint requires a valid dashboard name and may restrict access based on user roles and permissions.",
			httpMethod = "GET"
	)
	@ApiResponses(value = {
			@ApiResponse(code = 200, message = "OK - Successfully retrieved the dashboard details"),
			@ApiResponse(code = 403, message = "Forbidden - You do not have permission to access this resource"),
			@ApiResponse(code = 404, message = "Not Found - No dashboard found with the specified name"),
			@ApiResponse(code = 500, message = "Internal Server Error - An unexpected error occurred on the server")
	})
    public
    @ResponseBody
    ResponseEntity<?> getConfigurableDashboardByDashboardName(@ApiParam(value = "Dashboard Name") @PathVariable("dashboardName") final String dashboardName) {
        logger.debug("Received GET Dashboard Request by name: {}",dashboardName);
        List<Dashboard> configurableDashboard = this.dashboardService.checkDashboardExistsByName(dashboardName);
        if(!configurableDashboard.isEmpty()){
			logger.info("Found existing record for dashboard with name: {}",dashboardName);
			return new ResponseEntity<>(configurableDashboard, HttpStatus.OK);
        }else{
            logger.info("The DashBoard with given Name is not present, adding to errorList");
            throw ServiceException.badRequest(DashboardConstants.INSTANCE_IDENTIFIER + dashboardName + DealConstants.RECORD_NOT_AVAILABLE);
				}
    }
	//Create dashboard
	@Permittable(value = AcceptedTokenType.TENANT, groupId = PermittableGroupIds.CONFIGURATION_DASHBOARD_MANAGEMENT)
	@RequestMapping(
			value = "/dashboard",
			method = RequestMethod.POST,
			consumes = MediaType.APPLICATION_JSON_VALUE,
			produces = MediaType.APPLICATION_JSON_VALUE
	)
	@ApiOperation(
			value = "Create Configurable Dashboard",
			notes = "Creates a new configurable dashboard based on the provided request payload. " +
					"The dashboard can be customized with widgets, filters, and user preferences. " +
					"Only authorized users with the necessary permissions can create dashboards. " +
					"The request must include valid configuration details, otherwise, it may result in a validation error.",
			httpMethod = "POST"
	)
	@ApiResponses(value = {
			@ApiResponse(code = 200, message = "OK - Dashboard created successfully"),
			@ApiResponse(code = 201, message = "Created - The dashboard was successfully created"),
			@ApiResponse(code = 400, message = "Bad Request - Invalid request payload or missing required fields"),
			@ApiResponse(code = 403, message = "Forbidden - You do not have permission to create a dashboard"),
			@ApiResponse(code = 404, message = "Not Found - The requested resource does not exist"),
			@ApiResponse(code = 500, message = "Internal Server Error - An unexpected error occurred on the server")
	})
	public
	@ResponseBody
	ResponseEntity<?> createConfigurableDashboard(@ApiParam(value = "Dashboard Domain") @RequestBody final Dashboard dashboard) {
		logger.info("Received POST request to create a new Dashboard: {}", dashboard.getDashboardName());
		logger.info("Validation for User Role List");
		if(dashboard.getRoles().isEmpty()){
			logger.warn("No roles assigned to Dashboard: {}", dashboard.getDashboardName());
			throw ServiceException.badRequest(DashboardConstants.ROLE_LIST_NOT_FOUND);
		}
		final List<Dashboard> savedDashboard = this.dashboardService.checkDashboardExistsByName(dashboard.getDashboardName());
		if (!savedDashboard.isEmpty()) {
			logger.warn("Dashboard with name {} already exists. Aborting creation.", dashboard.getDashboardName());
			throw ServiceException.badRequest(DashboardConstants.INSTANCE_IDENTIFIER + dashboard.getDashboardName() + DashboardConstants.ALREADY_EXISTS);
		}
		logger.info("Processing AddDashboardCommand for Dashboard: {}", dashboard.getDashboardName());
		this.commandGateway.process(new AddDashboardCommand(dashboard));
		logger.info("Dashboard creation initiated for: {}", dashboard.getDashboardName());
		return ResponseEntity.accepted().build();
	}
	//Update dashboard
	@Permittable(value = AcceptedTokenType.TENANT, groupId = PermittableGroupIds.CONFIGURATION_DASHBOARD_MANAGEMENT)
	@RequestMapping(
			value = "/dashboard/{id}",
			method = RequestMethod.PUT,
			consumes = MediaType.APPLICATION_JSON_VALUE,
			produces = MediaType.APPLICATION_JSON_VALUE
	)
	@ApiOperation(
			value = "Update Configurable Dashboard By ID",
			notes = "Updates an existing configurable dashboard based on the provided dashboard ID. " +
					"Allows modifications to dashboard settings, widgets, filters, and user preferences. " +
					"Only authorized users with the necessary permissions can update a dashboard. " +
					"If the specified dashboard ID does not exist, a 'Not Found' response is returned.",
			httpMethod = "PUT"
	)
	@ApiResponses(value = {
			@ApiResponse(code = 200, message = "OK - Dashboard updated successfully"),
			@ApiResponse(code = 400, message = "Bad Request - Invalid request payload or missing required fields"),
			@ApiResponse(code = 403, message = "Forbidden - You do not have permission to update this dashboard"),
			@ApiResponse(code = 404, message = "Not Found - No dashboard found with the specified ID"),
			@ApiResponse(code = 500, message = "Internal Server Error - An unexpected error occurred on the server")
	})
	public
	@ResponseBody
	ResponseEntity<?> updateConfigurableDashboard(@ApiParam(value = "Dashboard ID") @PathVariable final Long id,
												  @ApiParam(value = "Dashboard Domain") @RequestBody Dashboard dashboard) {
        logger.info("Received PUT Dashboard Request for ID: {}",id);
		final Optional<Dashboard> savedDashboard = this.dashboardService.findById(id);
		if(!savedDashboard.isPresent()){
			logger.warn("Dashboard not found with ID: {}. Cannot proceed with update.", id);
			throw ServiceException.badRequest(DealConstants.RECORD_INSTANCE + id + DealConstants.RECORD_NOT_FOUND);
		}
		logger.debug("Dashboard found. Existing Dashboard Name: {}, Requested Update Name: {}", savedDashboard.get().getDashboardName(), dashboard.getDashboardName());
		this.commandGateway.process(new UpdateDashboardCommand(id, dashboard));
		logger.info("UpdateDashboardCommand processed successfully for Dashboard ID: {}", id);
		return ResponseEntity.accepted().build();
	}
	//Delete a dashboard
	@Permittable(value = AcceptedTokenType.TENANT, groupId = PermittableGroupIds.CONFIGURATION_DASHBOARD_MANAGEMENT)
	@RequestMapping(
			value = "/dashboard/{id}",
			method = RequestMethod.DELETE,
			consumes = MediaType.APPLICATION_JSON_VALUE,
			produces = MediaType.APPLICATION_JSON_VALUE
	)
	@ApiOperation(
			value = "Delete Configurable Dashboard By ID",
			notes = "Deletes an existing configurable dashboard based on the provided dashboard ID. " +
					"Only authorized users with the necessary permissions can perform this action. " +
					"Once deleted, the dashboard cannot be recovered. " +
					"If the specified dashboard ID does not exist, a 'Not Found' response is returned.",
			httpMethod = "DELETE"
	)
	@ApiResponses(value = {
			@ApiResponse(code = 200, message = "OK - Dashboard deleted successfully"),
			@ApiResponse(code = 204, message = "No Content - The dashboard was successfully deleted, and there is no response body"),
			@ApiResponse(code = 400, message = "Bad Request - Invalid request or missing required parameters"),
			@ApiResponse(code = 403, message = "Forbidden - You do not have permission to delete this dashboard"),
			@ApiResponse(code = 404, message = "Not Found - No dashboard found with the specified ID"),
			@ApiResponse(code = 500, message = "Internal Server Error - An unexpected error occurred on the server")
	})
	public
	@ResponseBody
	ResponseEntity<?> deleteConfigurableDashboard(@ApiParam(value = "dashboard ID") @PathVariable("id") final Long id) {
        logger.info("Received DELETE Dashboard Request for ID: {}",id);
		final Optional<Dashboard> savedDashboard = this.dashboardService.findById(id);
		if (!savedDashboard.isPresent()) {
			logger.warn("No dashboard found with ID: {}. Cannot proceed with deletion.", id);
			throw ServiceException.badRequest(DealConstants.RECORD_INSTANCE + id + DealConstants.DOESNT_EXIST);
		}
		logger.debug("Dashboard found for deletion: ID = {}, Name = {}", savedDashboard.get().getId(), savedDashboard.get().getDashboardName());
		this.commandGateway.process(new DeleteDashboardCommand(id));
		logger.info("Dashboard deletion command successfully dispatched for ID: {}", id);
		return ResponseEntity.accepted().build();
	}

	@Permittable(value = AcceptedTokenType.TENANT, groupId = PermittableGroupIds.CONFIGURATION_DASHBOARD_MANAGEMENT)
	@RequestMapping(
			value = "/dashboard/{id}",
			method = RequestMethod.GET,
			consumes = MediaType.ALL_VALUE,
			produces = MediaType.APPLICATION_JSON_VALUE
	)
	@ApiOperation(
			value = "Get Configurable Dashboard By ID",
			notes = "Retrieves details of a specific configurable dashboard based on the provided dashboard ID. " +
					"Dashboards contain business insights, analytics, and user-customized widgets. " +
					"This endpoint requires a valid dashboard ID and may restrict access based on user roles and permissions.",
			httpMethod = "GET"
	)
	@ApiResponses(value = {
			@ApiResponse(code = 200, message = "OK - Successfully retrieved the dashboard details"),
			@ApiResponse(code = 400, message = "Bad Request - Invalid or missing dashboard ID"),
			@ApiResponse(code = 403, message = "Forbidden - You do not have permission to access this dashboard"),
			@ApiResponse(code = 404, message = "Not Found - No dashboard found with the specified ID"),
			@ApiResponse(code = 500, message = "Internal Server Error - An unexpected error occurred on the server")
	})
	public
	@ResponseBody
	ResponseEntity<?> getConfigurableDashboardById(@ApiParam(value = "Dashboard ID") @PathVariable("id") final Long id) {
		logger.info("Received GET Dashboard Request for ID: {}",id);
		final Optional<Dashboard> savedDashboard = this.dashboardService.findById(id);
		if (!savedDashboard.isPresent()) {
			logger.warn("No dashboard found with ID: {}", id);
			throw ServiceException.badRequest(DealConstants.RECORD_INSTANCE + id + DealConstants.DOESNT_EXIST);
		}
		logger.debug("Dashboard found with ID: {}, Name: {}", id, savedDashboard.get().getDashboardName());
		final List<Dashboard> savedUSerRoleWiseDashboard = this.dashboardService.checkDashboardExistsByName(savedDashboard.get().getDashboardName());
		if (savedUSerRoleWiseDashboard.isEmpty()) {
			logger.warn("No dashboards found for the name '{}' associated with ID: {}", savedDashboard.get().getDashboardName(), id);
			throw ServiceException.badRequest(DashboardConstants.INSTANCE_IDENTIFIER + id + DealConstants.RECORD_NOT_AVAILABLE);
		}
		logger.info("Successfully retrieved dashboard with ID: {}", id);
		return new ResponseEntity<>(savedDashboard.get(), HttpStatus.OK);
	}

	@Permittable(value = AcceptedTokenType.TENANT, groupId = PermittableGroupIds.CONFIGURATION_DASHBOARD_MANAGEMENT)
	@RequestMapping(
			value = "/dashboard/order",
			method = RequestMethod.PUT,
			consumes = MediaType.APPLICATION_JSON_VALUE,
			produces = MediaType.APPLICATION_JSON_VALUE
	)
	@ApiOperation(
			value = "Update Dashboard Order",
			notes = "Updates the display order of dashboards based on user preferences or administrative settings. " +
					"This endpoint allows users to rearrange dashboards for better accessibility. " +
					"Only authorized users can modify the order. If the specified dashboard ID is invalid, " +
					"an appropriate error response will be returned.",
			httpMethod = "PUT"
	)
	@ApiResponses(value = {
			@ApiResponse(code = 200, message = "OK - Dashboard order updated successfully"),
			@ApiResponse(code = 204, message = "No Content - The update was successful, but there is no response body"),
			@ApiResponse(code = 400, message = "Bad Request - Invalid request payload or missing required parameters"),
			@ApiResponse(code = 403, message = "Forbidden - You do not have permission to update the dashboard order"),
			@ApiResponse(code = 404, message = "Not Found - No dashboard found with the specified ID"),
			@ApiResponse(code = 500, message = "Internal Server Error - An unexpected error occurred on the server")
	})
	public
	@ResponseBody
	ResponseEntity<?> updateConfigurableDashboardOrder(@ApiParam(value = "DashboardOrder Domain")
													   @RequestBody List<DashboardOrder> dashboardOrders) {
		logger.info("Received PUT request to update configurable dashboard order. Total dashboards to reorder: {}", dashboardOrders.size());
		CommandCallback<Long> command = null;
		try {
			logger.info("Processing UpdateDashboardOrderCommand to CommandGateway...");
			command = this.commandGateway.process(new UpdateDashboardOrderCommand(dashboardOrders), Long.class);
			logger.info("UpdateDashboardOrderCommand processed successfully.");
		} catch (CommandProcessingException commandProcessingException) {
			throw ServiceException.badRequest(commandProcessingException.getCause().getMessage());
		}
		try {
			if (command.get()!=null) {
				logger.info("Dashboard order updated successfully. Updated count: {}", command.get());
				return new ResponseEntity<>(command.get(), HttpStatus.OK);
			}
		} catch (InterruptedException interruptedException) {
			logger.error("InterruptedException occurred while awaiting dashboard order update result", interruptedException);
			throw ServiceException.badRequest(interruptedException.getCause().getMessage());
		}
		catch ( ExecutionException executionException) {
			logger.error("ExecutionException occurred while awaiting dashboard order update result", executionException);
			throw ServiceException.badRequest(executionException.getCause().getMessage());
		}
		logger.warn("No dashboard order update result returned. Throwing not found.");
		throw ServiceException.notFound("not found");
	}

	@Permittable(value = AcceptedTokenType.TENANT, groupId = PermittableGroupIds.CONFIGURATION_DASHBOARD_MANAGEMENT)
	@PostMapping(
			value = "/dashboard/clone",
			consumes = MediaType.APPLICATION_JSON_VALUE,
			produces = MediaType.APPLICATION_JSON_VALUE
	)
	@ApiOperation(
			value = "Clone Configurable Dashboard",
			notes = "Creates a new configurable dashboard by cloning an existing one. " +
					"This endpoint allows users to duplicate the configuration, widgets, filters, and user preferences of a current dashboard. " +
					"Only authorized users with the necessary permissions can clone dashboards. " +
					"If the specified dashboard to clone is not found, a 'Not Found' response is returned.",
			httpMethod = "POST"
	)
	@ApiResponses(value = {
			@ApiResponse(code = 200, message = "OK - Dashboard cloned successfully"),
			@ApiResponse(code = 201, message = "Created - New dashboard created successfully"),
			@ApiResponse(code = 400, message = "Bad Request - Invalid request payload or missing required fields"),
			@ApiResponse(code = 403, message = "Forbidden - You do not have permission to clone the dashboard"),
			@ApiResponse(code = 404, message = "Not Found - The dashboard to clone does not exist"),
			@ApiResponse(code = 500, message = "Internal Server Error - An unexpected error occurred on the server")
	})
	public
	@ResponseBody
	ResponseEntity<?> cloneDashboard(@ApiParam(value = "Dashboard Domain") @RequestBody final CloneDashboard cloneDashboard) {
		logger.info("Received request to clone dashboard with ID: {}", cloneDashboard.getId());
		final Optional<Dashboard> savedDashboard = this.dashboardService.findById(cloneDashboard.getId());
		if (!savedDashboard.isPresent()) {
			logger.warn("Dashboard with ID: {} does not exist. Aborting clone operation.", cloneDashboard.getId());
			throw ServiceException.badRequest(DealConstants.RECORD_INSTANCE + cloneDashboard.getId() + DealConstants.DOESNT_EXIST);
		}
		logger.debug("Dashboard exists. Proceeding to clone using CloneDashboardCommand.");
		this.commandGateway.process(new CloneDashboardCommand(cloneDashboard));
		logger.info("CloneDashboardCommand successfully processed for dashboard ID: {}", cloneDashboard.getId());
		return ResponseEntity.accepted().build();
	}
}
