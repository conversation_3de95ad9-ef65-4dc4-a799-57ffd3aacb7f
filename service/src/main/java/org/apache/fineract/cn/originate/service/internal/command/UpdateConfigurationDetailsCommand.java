package org.apache.fineract.cn.originate.service.internal.command;

import org.apache.fineract.cn.originate.api.v1.domain.configurationDetails.ConfigurationDetails;

public class UpdateConfigurationDetailsCommand {
    private final Long id;


    private final ConfigurationDetails configurationDetails;


    public UpdateConfigurationDetailsCommand(final Long id, final ConfigurationDetails configurationDetails) {
        super();
        this.id = id;
        this.configurationDetails = configurationDetails;
    }

    public ConfigurationDetails getConfigurationDetails() {
        return this.configurationDetails;
    }

    public Long getId() {
        return id;
    }


    @Override
    public String toString() {
        return "UpdateConfigurationDetailsCommand{" +
                "id=" + id +
                ", configurationDetails=" + configurationDetails +
                '}';
    }
}
