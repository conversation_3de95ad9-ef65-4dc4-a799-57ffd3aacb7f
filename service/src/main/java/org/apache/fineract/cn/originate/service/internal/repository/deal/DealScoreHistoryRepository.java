package org.apache.fineract.cn.originate.service.internal.repository.deal;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.List;
import java.util.Optional;

public interface DealScoreHistoryRepository extends JpaRepository<DealScoreHistoryEntity, Long> {

	Optional<DealScoreHistoryEntity> findById(Long id);

	@Query("select score from DealScoreHistoryEntity score where score.dealId = :dealId " +
			"and (:stageName IS NULL OR score.stageName = :stageName) order by score.version desc")
	List<DealScoreHistoryEntity> findByStageNameAndDealIdOrderByVersionDesc(@Param("stageName") String stageName,
																			@Param("dealId") Long dealId);

}
