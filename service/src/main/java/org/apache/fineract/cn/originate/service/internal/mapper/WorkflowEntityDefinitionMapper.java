package org.apache.fineract.cn.originate.service.internal.mapper;

import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import org.apache.fineract.cn.originate.api.v1.domain.workflow.WorkflowEntityDefinition;
import org.apache.fineract.cn.originate.service.internal.repository.workflow.WorkflowEntityDefinitionJPA;

public class WorkflowEntityDefinitionMapper {
    public static WorkflowEntityDefinitionJPA map(final WorkflowEntityDefinition entityDefinition){
    	
    	WorkflowEntityDefinitionJPA entityDefinitionEntity = new WorkflowEntityDefinitionJPA();
    	entityDefinitionEntity.setEntityName(entityDefinition.getEntityName());
    	entityDefinitionEntity.setEntityType(entityDefinition.getEntityType());
    	entityDefinitionEntity.setSubType(entityDefinition.getSubType());
    	
        return entityDefinitionEntity;
    }
    public static WorkflowEntityDefinition map(WorkflowEntityDefinitionJPA entityDefinitionEntity){
    	
    	WorkflowEntityDefinition entityDefinition = new WorkflowEntityDefinition();
    	entityDefinition.setId(entityDefinitionEntity.getId());
    	entityDefinition.setEntityName(entityDefinitionEntity.getEntityName());
    	entityDefinition.setEntityType(entityDefinitionEntity.getEntityType());
    	entityDefinition.setSubType(entityDefinitionEntity.getSubType());
    	entityDefinition.setCreatedBy(entityDefinitionEntity.getCreatedBy());
    	entityDefinition.setModifiedBy(entityDefinitionEntity.getModifiedBy());
    	entityDefinition.setCreatedDate(entityDefinitionEntity.getCreatedDate() != null ? entityDefinitionEntity.getCreatedDate().format(DateTimeFormatter.ISO_LOCAL_DATE) : null);
    	entityDefinition.setModifiedDate(entityDefinitionEntity.getModifiedDate() != null
                ? (entityDefinitionEntity.getModifiedDate().format(DateTimeFormatter.ISO_LOCAL_DATE))
                : null);
    	
    	return entityDefinition;
    
    }
    
   

	public static List<WorkflowEntityDefinition> map(List<WorkflowEntityDefinitionJPA> entityDefinitionEntities) {
		final  List<WorkflowEntityDefinition> entityDefinitionList = new ArrayList<>(entityDefinitionEntities.size());
		entityDefinitionList.addAll(entityDefinitionEntities.stream().map(WorkflowEntityDefinitionMapper::map).collect(Collectors.toList()));
		return entityDefinitionList;
	}
	

}
