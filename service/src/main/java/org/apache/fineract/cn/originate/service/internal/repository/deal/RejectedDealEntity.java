package org.apache.fineract.cn.originate.service.internal.repository.deal;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.fineract.cn.postgresql.util.LocalDateTimeConverter;
import org.eclipse.persistence.config.CacheIsolationType;

import javax.persistence.*;
import java.time.LocalDateTime;

@Entity
@Table(name = "originate_rejected_deal")
@org.eclipse.persistence.annotations.Cache(isolation = CacheIsolationType.ISOLATED)
@Data
@NoArgsConstructor
@ApiModel(value = "Originate Rejected Deal Model", description = "This Class stores Rejected Deal Details.")
public class RejectedDealEntity {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    @ApiModelProperty(value = "ID")
    private Long id;
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name="deal_id")
    @ApiModelProperty(value = "Deal ID")
    private DealEntity dealEntity;
    @Column(name = "stage_name")
    @ApiModelProperty(value = "Stage Name")
    private String stageName;
    @Column(name = "rejected_reason")
    @ApiModelProperty(value = "Rejected Reason")
    private String rejectedReason;
    @Column(name = "rejected_date")
    @Convert(converter = LocalDateTimeConverter.class)
    @ApiModelProperty(value = "Rejected Date")
    private LocalDateTime rejectedDate;
    @Column(name = "reopen_date")
    @Convert(converter = LocalDateTimeConverter.class)
    @ApiModelProperty(value = "Reopen Date")
    private LocalDateTime reopenDate;
    @Column(name = "created_by")
    @ApiModelProperty(value = "Created By")
    private String createdBy;
    @Column(name = "created_date")
    @Convert(converter = LocalDateTimeConverter.class)
    @ApiModelProperty(value = "Created Date")
    private LocalDateTime createdDate;
    @Column(name = "modified_by")
    @ApiModelProperty(value = "Modified By")
    private String modifiedBy;
    @Column(name = "modified_date")
    @Convert(converter = LocalDateTimeConverter.class)
    @ApiModelProperty(value = "Modified Date")
    private LocalDateTime modifiedDate;

    /* Saved Rejected Deal Constructor */
    public RejectedDealEntity(DealEntity dealEntity, String stageName, String rejectedReason, LocalDateTime rejectedDate, String createdBy, LocalDateTime createdDate) {
        this.dealEntity = dealEntity;
        this.stageName = stageName;
        this.rejectedReason = rejectedReason;
        this.rejectedDate = rejectedDate;
        this.createdBy = createdBy;
        this.createdDate = createdDate;
    }

    /* Saved Reopen Deal Constructor */
    public RejectedDealEntity(DealEntity dealEntity, String stageName, LocalDateTime reopenDate, String createdBy, LocalDateTime createdDate) {
        this.dealEntity = dealEntity;
        this.stageName = stageName;
        this.reopenDate = reopenDate;
        this.createdBy = createdBy;
        this.createdDate = createdDate;
    }
}
