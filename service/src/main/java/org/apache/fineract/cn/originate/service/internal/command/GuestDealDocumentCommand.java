package org.apache.fineract.cn.originate.service.internal.command;


import lombok.Data;

@Data
public class GuestDealDocumentCommand {

    private final String token;
    private final String id;
    private final String identifier;
    public GuestDealDocumentCommand(final String token, final String id, final String identifier) {
        super();
        this.token = token;
        this.id = id;
        this.identifier = identifier;
    }
}
