package org.apache.fineract.cn.originate.service.internal.repository.SpringBatch;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.Optional;
@Repository
public interface OriginateBatchConfigurationRepository extends JpaRepository<OriginateBatchConfigurationEntity, Long> {
   Optional <OriginateBatchConfigurationEntity> findByConfigurationName(String configurationName);
}
