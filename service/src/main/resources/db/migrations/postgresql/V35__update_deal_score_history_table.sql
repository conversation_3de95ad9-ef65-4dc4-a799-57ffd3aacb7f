
-- -----------------------------------------------------------------------
-- Add deal_analysis_details column in originate_deal_score_history table
-- -----------------------------------------------------------------------

ALTER TABLE originate_deal_score_history ADD column if not exists deal_analysis_details varchar NULL;

-- -------------------------------------------------------------
-- Move deal user analysis data into deal level for Hold stage
-- -------------------------------------------------------------

update
	originate_deal_score_history odsh
set
	deal_analysis_details = sb.deal_analysis_details from (
select 
odush.deal_analysis_details, odsb.id 
from
	originate_deal_user_score_history odush,
	originate_deal_team odt,
	originate_deal_score_history odsb
where odsb.id = odush.deal_score_id 
and odush.deal_team_id = odt.id 
and odt.team_lead = true
) as sb
where odsh.id = sb.id;

-- ---------------------------------------------------------------------------
-- Delete deal_analysis_details column from originate_deal_user_score_history
-- ---------------------------------------------------------------------------

ALTER TABLE originate_deal_user_score_history DROP COLUMN deal_analysis_details;


