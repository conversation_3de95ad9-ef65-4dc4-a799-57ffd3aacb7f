package org.apache.fineract.cn.originate.util.entity;

import com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.fineract.cn.originate.api.v1.domain.entity.EntityDefinition;
import org.apache.fineract.cn.originate.api.v1.domain.entity.EntityWorkflow;
import org.apache.fineract.cn.originate.api.v1.domain.entity.EntityWorkflowExecutionWrapper;
import org.apache.fineract.cn.originate.constants.entityDefinition.EntityDefinitionData;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

public class EntityDefinitionGenerator {

    public static EntityDefinition createEntityDefinitionForCompany() throws IOException {
        EntityDefinition entityDefinition = new EntityDefinition();
        ObjectMapper mapper = new ObjectMapper();
        entityDefinition.setId(EntityDefinitionData.ENTITY_ID);
        entityDefinition.setEntityName(EntityDefinitionData.ENTITY_NAME);
        entityDefinition.setEntityType(EntityDefinitionData.ENTITY_TYPE);
        entityDefinition.setSubType(EntityDefinitionData.ENTITY_SUBTYPE);
        entityDefinition.setVersion(EntityDefinitionData.ENTITY_VERSION);
        entityDefinition.setStatus(EntityDefinitionData.ENTITY_STATUS);
        entityDefinition.setEntityStatus(EntityDefinitionData.STATUS);
        entityDefinition.setEntityDetail(mapper.readTree(EntityDefinitionData.COMPANY_DETAILS));
        return entityDefinition;
    }

    public static EntityDefinition createEntityDefinitionForPerson() throws IOException {
        EntityDefinition entityDefinition = new EntityDefinition();
        ObjectMapper mapper = new ObjectMapper();
        entityDefinition.setId(EntityDefinitionData.ENTITY_ID_PERSON);
        entityDefinition.setEntityName(EntityDefinitionData.ENTITY_NAME_FOR_PERSON);
        entityDefinition.setEntityType(EntityDefinitionData.ENTITY_TYPE_PERSON);
        entityDefinition.setSubType(EntityDefinitionData.ENTITY_SUBTYPE);
        entityDefinition.setVersion(EntityDefinitionData.ENTITY_VERSION);
        entityDefinition.setStatus(EntityDefinitionData.ENTITY_STATUS);
        entityDefinition.setEntityStatus(EntityDefinitionData.STATUS);
        entityDefinition.setEntityDetail(mapper.readTree(EntityDefinitionData.PERSON_DETAILS));
        return entityDefinition;
    }

    public static EntityDefinition createDefaultEntityDefinitionForPerson() throws IOException {
        EntityDefinition entityDefinition = new EntityDefinition();
        ObjectMapper mapper = new ObjectMapper();
        entityDefinition.setId(EntityDefinitionData.ENTITY_ID_PERSON);
        entityDefinition.setEntityName(EntityDefinitionData.ENTITY_TYPE_PERSON);
        entityDefinition.setEntityType(EntityDefinitionData.ENTITY_TYPE_PERSON);
        entityDefinition.setSubType(EntityDefinitionData.ENTITY);
        entityDefinition.setVersion(EntityDefinitionData.ENTITY_VERSION);
        entityDefinition.setStatus(EntityDefinitionData.ENTITY_STATUS);
        entityDefinition.setEntityStatus(EntityDefinitionData.STATUS);
        entityDefinition.setEntityDetail(mapper.readTree(EntityDefinitionData.PERSON_DETAILS));
        return entityDefinition;
    }

    public static EntityDefinition createDefaultEntityDefinitionForCompany() throws IOException {
        EntityDefinition entityDefinition = new EntityDefinition();
        ObjectMapper mapper = new ObjectMapper();
        entityDefinition.setId(EntityDefinitionData.ENTITY_ID);
        entityDefinition.setEntityName(EntityDefinitionData.ENTITY_TYPE);
        entityDefinition.setEntityType(EntityDefinitionData.ENTITY_TYPE);
        entityDefinition.setSubType(EntityDefinitionData.ENTITY);
        entityDefinition.setVersion(EntityDefinitionData.ENTITY_VERSION);
        entityDefinition.setStatus(EntityDefinitionData.ENTITY_STATUS);
        entityDefinition.setEntityStatus(EntityDefinitionData.STATUS);
        entityDefinition.setEntityDetail(mapper.readTree(EntityDefinitionData.COMPANY_DETAILS));
        return entityDefinition;
    }

    public static List<EntityWorkflow> updateEntityWorkflowDetails(EntityDefinition entityDefinitionToUpdate) {
        List<EntityWorkflow> entityWorkflowList = new ArrayList<>();
        EntityWorkflow entityWorkflowOne = new EntityWorkflow();
        entityWorkflowOne.setDataModelId(1L);
        entityWorkflowOne.setEvent(EntityDefinitionData.WORKFLOW_ONE_EVENT.getEvent());
        entityWorkflowOne.setWorkflowName(EntityDefinitionData.WORKFLOW_ONE_NAME);
        entityWorkflowOne.setSequenceNumber(1L);
        entityWorkflowOne.setIsSelected(Boolean.TRUE);
        entityWorkflowList.add(entityWorkflowOne);

        EntityWorkflow entityWorkflowTwo = new EntityWorkflow();
        entityWorkflowTwo.setDataModelId(1L);
        entityWorkflowTwo.setEvent(EntityDefinitionData.WORKFLOW_ONE_EVENT.getEvent());
        entityWorkflowTwo.setWorkflowId(EntityDefinitionData.WORKFLOW_TWO_ID);
        entityWorkflowTwo.setWorkflowName(EntityDefinitionData.WORKFLOW_ONE_NAME);
        entityWorkflowTwo.setWorkflowVersion(EntityDefinitionData.WORKFLOW_TWO_VERSION);
        entityWorkflowTwo.setSequenceNumber(2L);
        entityWorkflowTwo.setIsSelected(Boolean.TRUE);
        entityWorkflowList.add(entityWorkflowTwo);

        return entityWorkflowList;

    }

    public static EntityWorkflowExecutionWrapper createWorkflowExecutionWrapper(EntityDefinition entityDefinition){
        EntityWorkflowExecutionWrapper entityWorkflowExecutionWrapper = new EntityWorkflowExecutionWrapper();
        entityWorkflowExecutionWrapper.setCustomerId(1L);
        entityWorkflowExecutionWrapper.setEntityWorkflowList(updateEntityWorkflowDetails(entityDefinition));
        return entityWorkflowExecutionWrapper;
    }
}
